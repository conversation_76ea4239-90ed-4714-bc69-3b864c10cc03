import {
  setupE2ETestHooks,
  executeCommand,
  expectNotice,
  expectPostFile,
  createManagedPostManager
} from '../helpers/shared-context';
import {
  completeModalInteraction,
  waitForModalType,
  fillModalInput,
  clickModalAction,
  waitForModalToClose
} from '../helpers/modal-helpers';
import {
  openGhostTab,
  clickSyncButton,
  waitForGhostTabStatus,
  waitForSyncToComplete,
  getGhostTabSyncStatus
} from '../helpers/ghost-tab-helpers';
import { trackUICreatedPost } from '../helpers/ghost-api-helpers';
import { test, expect } from 'vitest';

describe("Excerpt Editing", () => {
  const context = setupE2ETestHooks();
  const postManager = createManagedPostManager();

  test("should open excerpt edit modal when clicking excerpt edit button", async () => {
    // Create and sync a test post first
    const testTitle = "Test Post for Excerpt";

    await executeCommand(context, 'Ghost Sync: Create new post');
    await completeModalInteraction(
      context.page,
      'create-post',
      { 'post-title': testTitle },
      'submit'
    );

    await expectNotice(context, "Created new post");
    await openGhostTab(context);
    await waitForGhostTabStatus(context.page, 'new-post');

    // Sync the post to Ghost
    await clickSyncButton(context.page);
    await waitForSyncToComplete(context.page);
    await expectNotice(context, "Synced");

    // Track the post for cleanup
    await trackUICreatedPost(postManager(), testTitle);

    // Wait for the excerpt section to appear
    await context.page.waitForSelector('.ghost-excerpt-section', { timeout: 10000 });

    // Click the excerpt edit button
    const excerptEditButton = await context.page.waitForSelector('.ghost-excerpt-edit-btn', { timeout: 5000 });
    await excerptEditButton.click();

    // Wait for the excerpt edit modal to appear
    const modal = await waitForModalType(context.page, 'excerpt-edit');
    expect(modal.found).toBe(true);
    expect(modal.modalType).toBe('excerpt-edit');

    // Verify modal elements are present
    const textarea = await context.page.waitForSelector('[data-input="excerpt"]', { timeout: 5000 });
    expect(textarea).toBeTruthy();

    const saveButton = await context.page.waitForSelector('[data-action="save"]', { timeout: 5000 });
    expect(saveButton).toBeTruthy();

    const cancelButton = await context.page.waitForSelector('[data-action="cancel"]', { timeout: 5000 });
    expect(cancelButton).toBeTruthy();

    // Close the modal
    await clickModalAction(context.page, 'cancel');
    await waitForModalToClose(context.page);
  });

  test("should save excerpt when editing through modal", async () => {
    // Create and sync a test post first
    const testTitle = "Test Post for Excerpt Save";
    const testExcerpt = "This is a test excerpt that should be saved to the Ghost post. It contains enough text to verify the functionality works properly.";

    await executeCommand(context, 'Ghost Sync: Create new post');
    await completeModalInteraction(
      context.page,
      'create-post',
      { 'post-title': testTitle },
      'submit'
    );

    await expectNotice(context, "Created new post");
    await openGhostTab(context);
    await waitForGhostTabStatus(context.page, 'new-post');

    // Sync the post to Ghost
    await clickSyncButton(context.page);
    await waitForSyncToComplete(context.page);
    await expectNotice(context, "successfully");

    // Track the post for cleanup
    await trackUICreatedPost(postManager(), testTitle);

    // Wait for the excerpt section to appear
    await context.page.waitForSelector('.ghost-excerpt-section', { timeout: 10000 });

    // Click the excerpt edit button
    const excerptEditButton = await context.page.waitForSelector('.ghost-excerpt-edit-btn', { timeout: 5000 });
    await excerptEditButton.click();

    // Wait for the excerpt edit modal to appear
    const modal = await waitForModalType(context.page, 'excerpt-edit');
    expect(modal.found).toBe(true);

    // Fill in the excerpt
    await fillModalInput(context.page, 'excerpt', testExcerpt);

    // Verify character count is displayed
    const charCount = await context.page.textContent('.excerpt-char-count');
    expect(charCount).toContain(`${testExcerpt.length}/300`);

    // Save the excerpt
    await clickModalAction(context.page, 'save');
    await waitForModalToClose(context.page);

    // Wait a bit for the API call to complete
    await context.page.waitForTimeout(2000);

    // Wait for the save operation to complete - check for either success or error
    try {
      await expectNotice(context, "Excerpt updated successfully", 15000);
    } catch (error) {
      // If success notice not found, check for error notice
      const notices = await context.page.evaluate(() => {
        const noticeElements = document.querySelectorAll('.notice');
        return Array.from(noticeElements).map(el => el.textContent);
      });
      console.log('Available notices:', notices);
      throw error;
    }

    // Wait for the excerpt text to be updated in the UI
    await context.page.waitForFunction(
      (expectedExcerpt) => {
        const excerptElement = document.querySelector('.ghost-excerpt-text');
        return excerptElement && excerptElement.textContent && excerptElement.textContent.includes(expectedExcerpt);
      },
      testExcerpt
    );

    // Verify the excerpt is displayed in the UI
    const excerptText = await context.page.textContent('.ghost-excerpt-text');
    expect(excerptText).toContain(testExcerpt);
  });

  test("should cancel excerpt editing without saving changes", async () => {
    // Create and sync a test post first
    const testTitle = "Test Post for Excerpt Cancel";

    await executeCommand(context, 'Ghost Sync: Create new post');
    await completeModalInteraction(
      context.page,
      'create-post',
      { 'post-title': testTitle },
      'submit'
    );

    await expectNotice(context, "Created new post");
    await openGhostTab(context);
    await waitForGhostTabStatus(context.page, 'new-post');

    // Sync the post to Ghost
    await clickSyncButton(context.page);
    await waitForSyncToComplete(context.page);
    await expectNotice(context, "successfully");

    // Track the post for cleanup
    await trackUICreatedPost(postManager(), testTitle);

    // Wait for the excerpt section to appear
    await context.page.waitForSelector('.ghost-excerpt-section', { timeout: 10000 });

    // Get the original excerpt text
    const originalExcerpt = await context.page.textContent('.ghost-excerpt-text');

    // Click the excerpt edit button
    const excerptEditButton = await context.page.waitForSelector('.ghost-excerpt-edit-btn', { timeout: 5000 });
    await excerptEditButton.click();

    // Wait for the excerpt edit modal to appear
    const modal = await waitForModalType(context.page, 'excerpt-edit');
    expect(modal.found).toBe(true);

    // Fill in some text
    await fillModalInput(context.page, 'excerpt', 'This text should not be saved');

    // Cancel the edit
    await clickModalAction(context.page, 'cancel');
    await waitForModalToClose(context.page);

    // Verify the excerpt text hasn't changed
    const currentExcerpt = await context.page.textContent('.ghost-excerpt-text');
    expect(currentExcerpt).toBe(originalExcerpt);
  });

  test("should save excerpt using save button", async () => {
    // Create and sync a test post first
    const testTitle = "Test Post for Save Button";
    const testExcerpt = "This excerpt will be saved using the save button.";

    await executeCommand(context, 'Ghost Sync: Create new post');
    await completeModalInteraction(
      context.page,
      'create-post',
      { 'post-title': testTitle },
      'submit'
    );

    await expectNotice(context, "Created new post");
    await openGhostTab(context);
    await waitForGhostTabStatus(context.page, 'new-post');

    // Sync the post to Ghost
    await clickSyncButton(context.page);
    await waitForSyncToComplete(context.page);
    await expectNotice(context, "successfully");

    // Track the post for cleanup
    await trackUICreatedPost(postManager(), testTitle);

    // Wait for the excerpt section to appear
    await context.page.waitForSelector('.ghost-excerpt-section', { timeout: 10000 });

    // Click the excerpt edit button
    const excerptEditButton = await context.page.waitForSelector('.ghost-excerpt-edit-btn', { timeout: 5000 });
    await excerptEditButton.click();

    // Wait for the excerpt edit modal to appear
    const modal = await waitForModalType(context.page, 'excerpt-edit');
    expect(modal.found).toBe(true);

    // Fill in the excerpt
    await fillModalInput(context.page, 'excerpt', testExcerpt);

    // Click the save button
    await clickModalAction(context.page, 'save');
    await waitForModalToClose(context.page);

    // Wait for the save operation to complete
    await expectNotice(context, "Excerpt updated successfully");

    // Verify the excerpt is displayed in the UI
    const excerptText = await context.page.textContent('.ghost-excerpt-text');
    expect(excerptText).toContain(testExcerpt);
  });

});
